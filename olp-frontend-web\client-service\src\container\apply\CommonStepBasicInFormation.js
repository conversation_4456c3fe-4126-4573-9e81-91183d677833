import React, { useEffect, useState, useCallback } from "react";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import { validatorFormAdditionalRule } from "../../helper/validatorFormAdditionalRule";
import CircularProgress from "@mui/material/CircularProgress";
import InputAdornment from "@mui/material/InputAdornment";
import FormControlLabel from "@mui/material/FormControlLabel";
import {
  getApply,
  getCommonCode,
  getCommonSubcode,
  getBranchCode,
  getBranchList,
  getBranchByEmpId,
  apply,
} from "../../helper/api";
import { TextValidator } from "react-material-ui-form-validator";
import { LoadingTemplate } from "../../component/LoadingTemplate";
import format from "date-fns/format";
import { makeStyles } from "@mui/styles";
import MenuItem from "@mui/material/MenuItem";
import Radio from "@mui/material/Radio";
import RadioGroup from "@mui/material/RadioGroup";
import FormControl from "@mui/material/FormControl";
import { useNavigate, useLocation } from "react-router-dom";
import CustomDatePicker from "../../component/CustomDatePicker";

const useStyles = makeStyles((theme) => ({
  color666: {
    color: "#666666",
  },
}));

export default function CommonStepBasicInFormation(props) {
  const classes = useStyles();
  const navigate = useNavigate();
  const [townList, setTownList] = useState({ list: [], isLoading: false });
  const [mailingTownList, setMailingTownList] = useState({
    list: [],
    isLoading: false,
  });
  const [companyTownList, setCompanyTownList] = useState({
    list: [],
    isLoading: false,
  });
  const [branchCityList, setBranchCityList] = useState([]);
  const [userSubTypeList, setUserSubTypeList] = useState([]);
  const [branchTownList, setBranchTownList] = useState({
    list: null,
    isLoading: false,
  });
  const [branchList, setBranchList] = useState({
    list: null,
    isLoading: false,
  });
  const [applyBranchList, setApplyBranchList] = useState(null);
  const [cityList, setCityList] = useState(null);
  const [jobTypeList, setJobTypeList] = useState(null);
  const [jobSubTypeList, setJobSubTypeList] = useState({
    list: [],
    isLoading: false,
  });
  const [bankType, setBankType] = useState(null);
  const [bankTypeCity, setBankTypeCity] = useState(null);

  // 預先填寫項目不能更改
  const [disabledIdNo, setDisabledIdNo] = useState(false);
  const [disabledBirthDate, setDisabledBirthDate] = useState(false);
  const [disabledMobileNumber, setDisabledMobileNumber] = useState(false);

  // 判斷是否爲青創案
  const [isYouthsLoan, setIsYouthsLoan] = useState(false);
  const [branchListForYouthsLoan, setBranchListForYouthsLoan] = useState(null);

  const {
    loanType,
    identityType,
    basicInfo,
    setBasicInfo,
    contactInfo,
    setContactInfo,
    jobInfo,
    setJobInfo,
    setErrorResp,
    userType,
    isLoading,
    setIsLoading,
    guaranteeInfo,
    setGuaranteeInfo,
    plan,
    checkPlanState,
    presetData,
  } = props;

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
    validatorFormAdditionalRule();
    fetchBasicInfo();
    if (userType === "guarantor") {
      if (loanType === "personalloan") {
        fetchSubData(userType, "pl-user-type");
      } else {
        fetchSubData(userType, "hl-user-type");
      }
    }
    console.log("data from url param : ", presetData);
  }, []);

  useEffect(() => {
    if (bankTypeCity !== null) {
      fetchBranchBankCity();
    }
  }, [bankTypeCity]);

  useEffect(() => {
    if (contactInfo.branchBankTownCode) {
      if(bankType){
        fetchBranchList(contactInfo.branchBankTownCode, bankType);
      }
    }
  }, [contactInfo.branchBankTownCode]);

  useEffect(() => {
    if (contactInfo.branchBankCityCode && bankTypeCity !== null) {
      fetchSubData(contactInfo.branchBankCityCode, bankTypeCity);
    }
  }, [contactInfo.branchBankCityCode]);

  useEffect(() => {
    if (applyBranchList !== null) {
      fetchJobAndContact();
    }
  }, [applyBranchList]);

  useEffect(() => {
    if (branchListForYouthsLoan !== null) {
      fetchJobAndContact();
    }
  }, [branchListForYouthsLoan]);

  useEffect(() => {
    if (branchList.list !== null) {
      if (contactInfo.branchBankCode) {
        branchList.list.forEach((e) => {
          e.code === contactInfo.branchBankCode &&
            setContactInfo({ ...contactInfo, branchBankAddress: e.address });
        });
      }
    }
  }, [branchList]);

  //此處打到中台獲得申請者的基本資料
  const fetchBasicInfo = async () => {
    try {
      let [personalBasicInfo] = await Promise.all([
        await getApply(`/loan/apply/getPersonalBasicInfo`, loanType, userType),
      ]);
      if (personalBasicInfo.data.stat === "ok") {
        if (personalBasicInfo.data.result.birthDate !== "") {
          setDisabledBirthDate(true);
          personalBasicInfo.data.result.birthDate = format(
            new Date(personalBasicInfo.data.result.birthDate),
            "yyyyMMdd"
          );
        }
        if (personalBasicInfo.data.result.idNo !== "") {
          setDisabledIdNo(true);
        }
        setBasicInfo(personalBasicInfo.data.result);
        if (
          loanType === "personalloan" &&
          personalBasicInfo.data.result.identities.includes("employee")
        ) {
          setBankType("emp-pl-branch-bank");
          setBankTypeCity("emp-pl-branch-bank-city");
        } else if (loanType === "personalloan") {
          setBankType("pl-branch-bank");
          setBankTypeCity("pl-branch-bank-city");
        } else {
          setBankType("hl-branch-bank");
          setBankTypeCity("hl-branch-bank-city");
        }
        if (window.sessionStorage.getItem("bno")) {
          setIsYouthsLoan(true);
          fetchBranchListForYouthsLoan();
        } else if (personalBasicInfo.data.result.identities.includes("loanee")) {
          fetchApplyBranchList();
        } else {
          fetchJobAndContact();
        }
      } else {
        let responses = [];
        personalBasicInfo.data.stat === "error" &&
          responses.push(personalBasicInfo.data);
        navigate("/error", { 
          state: { errorResp: responses, loanType: loanType }
        });
      }
    } catch (error) {
      navigate("/error", { 
        state: { errorResp: [], loanType: loanType }
      });
    }
  };

  //此處打到中台獲得申請者的工作相關資料
  const fetchJobAndContact = async () => {
    try {
      let [
        personalContactInfo,
        getPersonalJobInfo,
        getPersonalGuaranteeInfo,
        cityListRsp,
        jobTypeRsp,
      ] = await Promise.all([
        await getApply(
          `/loan/apply/getPersonalContactInfo`,
          loanType,
          userType
        ),
        await getApply(`/loan/apply/getPersonalJobInfo`, loanType, userType),
        await getApply(`/loan/apply/getPersonalGuaranteeInfo`, loanType),
        await getCommonCode("city"),
        // await getCommonCode("house-status"),
        await getCommonCode("job-type"),
      ]);
      if (
        personalContactInfo.data.stat === "ok" &&
        getPersonalJobInfo.data.stat === "ok" &&
        getPersonalGuaranteeInfo.data.stat === "ok" &&
        cityListRsp.data.stat === "ok" &&
        jobTypeRsp.data.stat === "ok"
      ) {
        console.log("fetchJobAndContact personalContactInfo.data : ",personalContactInfo.data);
        console.log("fetchJobAndContact getPersonalJobInfo.data : ",getPersonalJobInfo.data);
        console.log("fetchJobAndContact getPersonalGuaranteeInfo.data : ",getPersonalGuaranteeInfo.data);
        console.log("fetchJobAndContact cityListRsp.data : ",cityListRsp.data);
        console.log("fetchJobAndContact jobTypeRsp.data : ",jobTypeRsp.data);
        setCityList(cityListRsp.data.result);
        let tmpPersonalContactInfo = personalContactInfo.data.result;
        if (tmpPersonalContactInfo.mobileNumber !== "") {
          setDisabledMobileNumber(true);
        }
        if (applyBranchList !== null) {
          tmpPersonalContactInfo.branchBankCode = applyBranchList[0].code;
          tmpPersonalContactInfo.branchBankAddress = applyBranchList[0].address;
        } else {
          if (
            window.sessionStorage.getItem("cti") &&
            window.sessionStorage.getItem("twn") &&
            window.sessionStorage.getItem("dep")
          ) {
            tmpPersonalContactInfo.branchBankCityCode = window.sessionStorage.getItem(
              "cti"
            );
            tmpPersonalContactInfo.branchBankTownCode = window.sessionStorage.getItem(
              "twn"
            );
            tmpPersonalContactInfo.branchBankCode = window.sessionStorage.getItem(
              "dep"
            );
          }
        }

        if(branchListForYouthsLoan !== null) {
          // let backCode = window.sessionStorage.getItem("bno");
          let tempBranchListForYouthsLoan = branchListForYouthsLoan;

          /*
          // J-110-0091_08374_B1001
          if(!isBranchExist) {
            backCode ="020"; // for 中台api找不到資料時用預設020
            // backCode = tempBranchListForYouthsLoan[0].code; // 此方式爲取api回傳的第一個分行別
          }
          */
            // tmpPersonalContactInfo.branchBankCode = backCode;
            // tmpPersonalContactInfo.branchBankCode = tempBranchListForYouthsLoan.find(item => item.code = backCode).code; // 此處因爲用了find(),所以會去改變原本的內容,因此要另外用一個變數tempBranchListForYouthsLoan來存取,不然React會認爲狀態有改變,造成反覆渲染(Maximum update depth exceeded.)
            // tmpPersonalContactInfo.branchBankAddress = tempBranchListForYouthsLoan.find(item => item.code = backCode).address; // 此處因爲用了find(),所以會去改變原本的內容,因此要另外用一個變數tempBranchListForYouthsLoan來存取,不然React會認爲狀態有改變,造成反覆渲染(Maximum update depth exceeded.)
            tmpPersonalContactInfo.branchBankCode = tempBranchListForYouthsLoan[0].code; // 此處因爲用了find(),所以會去改變原本的內容,因此要另外用一個變數tempBranchListForYouthsLoan來存取,不然React會認爲狀態有改變,造成反覆渲染(Maximum update depth exceeded.)
            tmpPersonalContactInfo.branchBankAddress = tempBranchListForYouthsLoan[0].address; // 此處因爲用了find(),所以會去改變原本的內容,因此要另外用一個變數tempBranchListForYouthsLoan來存取,不然React會認爲狀態有改變,造成反覆渲染(Maximum update depth exceeded.)
        }

        setContactInfo(tmpPersonalContactInfo);
        if (personalContactInfo.data.result.branchBankCode !== "") {
          setBranchList({
            list: [
              {
                code: personalContactInfo.data.result.branchBankCode,
                name: personalContactInfo.data.result.branchBankName,
                address: personalContactInfo.data.result.branchBankAddress,
              },
            ],
            isLoading: false,
          });
        }
        setJobTypeList(jobTypeRsp.data.result);
        setJobInfo(getPersonalJobInfo.data.result);
        setGuaranteeInfo(getPersonalGuaranteeInfo.data.result);
        if (personalContactInfo.data.result.homeAddressCityCode !== "") {
          fetchSubData(
            personalContactInfo.data.result.homeAddressCityCode,
            "city"
          );
        }
        if (personalContactInfo.data.result.mailingAddressCityCode !== "") {
          fetchSubData(
            personalContactInfo.data.result.mailingAddressCityCode,
            "mailing-city"
          );
        }
        if (getPersonalJobInfo.data.result.companyAddressCityCode !== "") {
          fetchSubData(
            getPersonalJobInfo.data.result.companyAddressCityCode,
            "company-city"
          );
        }
        if (getPersonalJobInfo.data.result.jobType !== "") {
          fetchSubData(getPersonalJobInfo.data.result.jobType, "job-type");
        }
      } else {
        let responses = [];
        personalContactInfo.data.stat === "error" &&
          responses.push(personalContactInfo.data);
        getPersonalJobInfo.data.stat === "error" &&
          responses.push(getPersonalJobInfo.data);
        cityListRsp.data.stat === "error" && responses.push(cityListRsp.data);
        getPersonalGuaranteeInfo.data.stat === "error" &&
          responses.push(getPersonalGuaranteeInfo.data);
        jobTypeRsp.data.stat === "error" && responses.push(jobTypeRsp.data);
        navigate("/error", { 
          state: { errorResp: responses, loanType: loanType }
        });
      }
      setIsLoading(false);
    } catch (error) {
      navigate("/error", { 
        state: { errorResp: [], loanType: loanType }
      });
    }
  };

  //此處打到中台獲得分行所在城市資料
  const fetchBranchBankCity = async () => {
    try {
      let [bankTypeRsp] = await Promise.all([await getCommonCode(bankTypeCity)]);
      if (bankTypeRsp.data.stat === "ok") {
        setBranchCityList(bankTypeRsp.data.result);
      } else {
        let responses = [];
        bankTypeRsp.data.stat === "error" && responses.push(bankTypeRsp.data);
        navigate("/error", { 
          state: { errorResp: responses, loanType: loanType }
        });
      }
    } catch (error) {
      navigate("/error", { 
        state: { errorResp: [], loanType: loanType }
      });
    }
  };

  //此處打到中台獲得城市等其他資訊
  const fetchSubData = useCallback(
    async (codeId, codeType) => {
      if (codeType === "city") {
        setTownList({ ...townList, isLoading: true });
      } else if (codeType === "mailing-city") {
        setMailingTownList({ ...mailingTownList, isLoading: true });
      } else if (codeType === "job-type") {
        setJobSubTypeList({ ...jobSubTypeList, isLoading: true });
      } else if (codeType === bankTypeCity) {
        setBranchTownList({ ...branchTownList, isLoading: true });
      } else if (codeType === "company-city") {
        setCompanyTownList({ ...companyTownList, isLoading: true });
      }
      try {
        let result;
        if (
          codeType === "city" ||
          codeType === "mailing-city" ||
          codeType === "company-city"
        ) {
          result = await getCommonSubcode("city", codeId);
        } else {
          result = await getCommonSubcode(codeType, codeId);
        }
        if (result.data.stat === "ok") {
          console.log("fetchSubData result.data : ", result.data);
          if (codeType === "city") {
            setTownList({ list: result.data.result, isLoading: false });
          } else if (codeType === "mailing-city") {
            setMailingTownList({ list: result.data.result, isLoading: false });
          } else if (codeType === "job-type") {
            setJobSubTypeList({ list: result.data.result, isLoading: false });
          } else if (codeType === "company-city") {
            setCompanyTownList({ list: result.data.result, isLoading: false });
          } else if (
            codeType === "pl-user-type" ||
            codeType === "hl-user-type"
          ) {
            setUserSubTypeList(result.data.result);
          } else {
            setBranchTownList({ list: result.data.result, isLoading: false });
          }
        } else {
          let responses = [];
          responses.push(result.data);
          navigate("/error", { 
            state: { errorResp: responses, loanType: loanType }
          });
        }
      } catch (error) {
        // console.log(error);
        navigate("/error", { 
          state: { errorResp: [], loanType: loanType }
        });
      }
    },
    [bankType]
  );

  const fetchBranchList = useCallback(async (codeId, bankType) => {
    try {
      let tmpList = branchList;
      tmpList.isLoading = true;
      setBranchList({ ...tmpList });
      let result = await getBranchCode(codeId, bankType);
      if (result.data.stat === "ok") {
        setBranchList({ list: result.data.result, isLoading: false });
      } else {
        let responses = [];
        responses.push(result.data);
        navigate("/error", { 
          state: { errorResp: responses, loanType: loanType }
        });
      }
    } catch (error) {
      navigate("/error", { 
        state: { errorResp: [], loanType: loanType }
      });
    }
  }, []);

  const fetchApplyBranchList = useCallback(async () => {
    try {
      let result = await apply.post(`/loan/apply/getBranchBankList`);
      if (result.data.stat === "ok") {
        setApplyBranchList(result.data.result);
      } else {
        let responses = [];
        responses.push(result.data);
        navigate("/error", { 
          state: { errorResp: responses, loanType: loanType }
        });
      }
    } catch (error) {
      navigate("/error", { 
        state: { errorResp: [], loanType: loanType }
      });
    }
  }, []);

  const fetchBranchListForYouthsLoan = useCallback(async () => {
    try {
      let bno = window.sessionStorage.getItem("bno");
      let result = await getBranchList(bno);
      if (result.data.stat === "ok") {
        let bankList = result.data.result;
        setBranchListForYouthsLoan(bankList);
      } else {
        console.log('@3');
        let responses = [];
        responses.push(result.data);
        setErrorResp(responses);
      }
    } catch (error) {
      setErrorResp([]);
    }
  }, []);

  if (isLoading) {
    return <LoadingTemplate />;
  }

  //畫面總渲染
  return (
    <>
      <Grid container spacing={3}>
        <BasicInfo
          basicInfo={basicInfo}
          setBasicInfo={setBasicInfo}
          disabledBirthDate={disabledBirthDate}
          classes={classes}
          disabledIdNo={disabledIdNo}
          guaranteeInfo={guaranteeInfo}
          setGuaranteeInfo={setGuaranteeInfo}
          userSubTypeList={userSubTypeList}
          userType={userType}
          plan={plan}
          checkPlanState={checkPlanState}
        />
        { checkPlanState(plan) !== 1 && (
          <>
          <ContactInfo
          contactInfo={contactInfo}
          setContactInfo={setContactInfo}
          fetchSubData={fetchSubData}
          cityList={cityList}
          townList={townList}
          setMailingTownList={setMailingTownList}
          disabledMobileNumber={disabledMobileNumber}
          branchTownList={branchTownList}
          branchCityList={branchCityList}
          fetchBranchList={fetchBranchList}
          branchList={branchList}
          applyBranchList={applyBranchList}
          classes={classes}
          userType={userType}
          identityType={identityType}
          bankType={bankType}
          mailingTownList={mailingTownList}
          identities={basicInfo.identities}
          isYouthsLoan={isYouthsLoan}
          branchListForYouthsLoan={branchListForYouthsLoan}
          plan={plan}
          checkPlanState={checkPlanState}
          presetData={presetData}
          loanType={loanType}
        />
        <JobInfo
          loanType={loanType}
          jobInfo={jobInfo}
          setJobInfo={setJobInfo}
          jobSubTypeList={jobSubTypeList}
          fetchSubData={fetchSubData}
          jobTypeList={jobTypeList}
          cityList={cityList}
          companyTownList={companyTownList}
          plan={plan}
          checkPlanState={checkPlanState}
          presetData={presetData}
        />
          </>
      )}
        
      </Grid>
    </>
  );
}


//基本資料畫面結構
const BasicInfo = React.memo(function BasicInfo(props) {
  const {
    basicInfo,
    setBasicInfo,
    disabledBirthDate,
    disabledIdNo,
    classes,
    setErrorResp,
    guaranteeInfo,
    setGuaranteeInfo,
    userSubTypeList,
    userType,
    plan,
    checkPlanState,
  } = props;
  const [nationalityList, setNationalityList] = useState([]);
  const [educationLevelList, setEducationLevelList] = useState([]);
  const [marriageStatusList, setMarriageStatusList] = useState([]);
  const [guarantyReasonList, setGuarantyReasonList] = useState([]);
  const [relationBorrowerTypeList, setRelationBorrowerTypeList] = useState([]);
  const [codeApplySiteList, setCodeApplySiteList] = useState([]);
  const [codeApplyReasonList, setCodeApplyReasonList] = useState([]);

  // console.log("basic render");
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      let [
        educationLevelRsp,
        marriageStatusRsp,
        nationalityRsp,
        guarantyReasonRsp,
        relationBorrowerTypeRsp,
        codeApplySiteResp,
        codeApplyReasonResp,
      ] = await Promise.all([
        await getCommonCode("education-level"),
        await getCommonCode("marriage-status"),
        await getCommonCode("nationality"),
        await getCommonCode("guaranty-reason"),
        await getCommonCode("relation-borrower-type"),
        await getCommonCode("code-apply-site"),
        await getCommonCode("code-apply-reason")
      ]);
      if (
        educationLevelRsp.data.stat === "ok" &&
        marriageStatusRsp.data.stat === "ok" &&
        nationalityRsp.data.stat === "ok" &&
        guarantyReasonRsp.data.stat === "ok" &&
        relationBorrowerTypeRsp.data.stat === "ok" &&
        codeApplySiteResp.data.stat === "ok" &&
        codeApplyReasonResp.data.stat === "ok"
      ) {
        console.log("fetchData educationLevelRsp sult.data : ",educationLevelRsp.data);
        console.log("fetchData marriageStatusRsp sult.data : ",marriageStatusRsp.data);
        setEducationLevelList(educationLevelRsp.data.result);
        setMarriageStatusList(marriageStatusRsp.data.result);
        setNationalityList(nationalityRsp.data.result);
        setGuarantyReasonList(guarantyReasonRsp.data.result);
        setRelationBorrowerTypeList(relationBorrowerTypeRsp.data.result);
        setCodeApplySiteList(codeApplySiteResp.data.result);
        setCodeApplyReasonList(codeApplyReasonResp.data.result);
      } else {
        let responses = [];
        educationLevelRsp.data.stat === "error" &&
          responses.push(educationLevelRsp.data);
        marriageStatusRsp.data.stat === "error" &&
          responses.push(marriageStatusRsp.data);
        nationalityRsp.data.stat === "error" &&
          responses.push(nationalityRsp.data);
        guarantyReasonRsp.data.stat === "error" &&
          responses.push(guarantyReasonRsp.data);
        relationBorrowerTypeRsp.data.stat === "error" &&
          responses.push(relationBorrowerTypeRsp.data);
        codeApplySiteResp.data.stat === "error" &&
        responses.push(codeApplySiteResp.data);
        codeApplyReasonResp.data.stat === "error" &&
        responses.push(codeApplyReasonResp.data);
        setErrorResp(responses);
      }
    } catch (error) {
      setErrorResp([]);
    }
  };

  return (
    <>
      <Grid item xs={12}>
        <Typography variant="h6">基本資料</Typography>
      </Grid>
      { checkPlanState(plan) !== 1 && (
          <>
            <Grid item xs={12}>
              <TextValidator
                  validators={["required", "isNotNumber", "maxStringLength:50", 'isChinese']}
                  errorMessages={["請輸入姓名", "不可輸入數字", "字數超過長度限制", "請輸入中文"]}
                  id="name"
                  name="name"
                  inputProps={{
                    maxLength: 50,
                  }}
                  label="姓名(必填)"
                  fullWidth
                  autoComplete="name"
                  variant="outlined"
                  value={basicInfo.name}
                  onChange={(e) => {
                    setBasicInfo({ ...basicInfo, name: e.target.value });
                  }}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2">英文姓名</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextValidator
                  validators={["validEnglishName", "maxStringLength:100"]}
                  errorMessages={["請輸入英文", "字數超過長度限制"]}
                  id="engFirstName"
                  name="engFirstName"
                  label="英文姓"
                  fullWidth
                  inputProps={{
                    maxLength: 100,
                  }}
                  autoComplete="engFirstName"
                  variant="outlined"
                  value={basicInfo.engFirstName}
                  onChange={(e) => {
                    setBasicInfo({
                      ...basicInfo,
                      engFirstName: e.target.value.toUpperCase(),
                    });
                  }}
              />
              <Typography variant="body2" className={classes.color666}>
                請填寫與護照相同姓名
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextValidator
                  validators={["validEnglishName", "maxStringLength:100"]}
                  errorMessages={["請輸入英文", "字數超過長度限制"]}
                  id="engLastName"
                  name="engLastName"
                  label="英文名"
                  fullWidth
                  autoComplete="engLastName"
                  variant="outlined"
                  value={basicInfo.engLastName}
                  inputProps={{
                    maxLength: 100,
                  }}
                  onChange={(e) => {
                    setBasicInfo({
                      ...basicInfo,
                      engLastName: e.target.value.toUpperCase(),
                    });
                  }}
              />
            </Grid>
          </>
      )}
      <Grid item xs={12}>
        <TextValidator
          id="idNo"
          name="idNo"
          validators={["required", "maxStringLength:10", "isIdNo"]}
          errorMessages={[
            "請輸入身分證字號",
            "字數超過長度限制",
            "請輸入正確格式",
          ]}
          label="身分證字號(必填)"
          fullWidth
          autoComplete="idNo"
          variant="outlined"
          inputProps={{
            maxLength: 10,
          }}
          disabled={disabledIdNo}
          value={basicInfo.idNo}
          onChange={(e) => {
            setBasicInfo({
              ...basicInfo,
              idNo: e.target.value.toUpperCase(),
            });
          }}
        />
      </Grid>
      {(userType === "borrower" && (checkPlanState(plan) === 1 || checkPlanState(plan) === 2)) && (
          <>
            <Grid item xs={12}>
              <Typography variant="body2">身分證換補領資訊</Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2" style={{ marginBottom: '8px' }}>發證日期(必填)</Typography>
            </Grid>
            <Grid item xs={4}>
              <TextValidator
                  name="applyYear"
                  label="年"
                  value={(() => {
                    if (!basicInfo || !basicInfo.applyDate || basicInfo.applyDate.length < 8) return '';
                    return basicInfo.applyDate.substring(0, 4) || '';
                  })()}
                  onChange={(e) => {
                    const year = e.target.value;
                    const currentMonth = (basicInfo.applyDate && basicInfo.applyDate.length >= 8) ? basicInfo.applyDate.substring(4, 6) : '01';
                    const currentDay = (basicInfo.applyDate && basicInfo.applyDate.length >= 8) ? basicInfo.applyDate.substring(6, 8) : '01';
                    const newDate = year && currentMonth && currentDay ? `${year}${currentMonth.padStart(2, '0')}${currentDay.padStart(2, '0')}` : '';
                    setBasicInfo({
                      ...basicInfo,
                      applyDate: newDate,
                    });
                  }}
                  fullWidth
                  select={true}
                  variant="outlined"
                  validators={["required"]}
                  errorMessages={["請選擇年份"]}
              >
                {(() => {
                  const currentYear = new Date().getFullYear();
                  const startYear = 2005; // 民國94年 = 西元2005年
                  const years = [];
                  for (let year = startYear; year <= currentYear; year++) {
                    years.push(
                      <MenuItem value={year.toString()} key={year}>
                        {year}年
                      </MenuItem>
                    );
                  }
                  return years;
                })()}
              </TextValidator>
            </Grid>
            <Grid item xs={4}>
              <TextValidator
                  name="applyMonth"
                  label="月"
                  value={(() => {
                    if (!basicInfo || !basicInfo.applyDate || basicInfo.applyDate.length < 8) return '';
                    return parseInt(basicInfo.applyDate.substring(4, 6)).toString() || '';
                  })()}
                  onChange={(e) => {
                    const month = e.target.value;
                    const currentYear = (basicInfo.applyDate && basicInfo.applyDate.length >= 8) ? basicInfo.applyDate.substring(0, 4) : new Date().getFullYear().toString();
                    const currentDay = (basicInfo.applyDate && basicInfo.applyDate.length >= 8) ? basicInfo.applyDate.substring(6, 8) : '01';
                    const newDate = currentYear && month && currentDay ? `${currentYear}${month.padStart(2, '0')}${currentDay.padStart(2, '0')}` : '';
                    setBasicInfo({
                      ...basicInfo,
                      applyDate: newDate,
                    });
                  }}
                  fullWidth
                  select={true}
                  variant="outlined"
                  validators={["required"]}
                  errorMessages={["請選擇月份"]}
              >
                {[...Array(12)].map((_, index) => {
                  const month = index + 1;
                  return (
                    <MenuItem value={month.toString()} key={month}>
                      {month}月
                    </MenuItem>
                  );
                })}
              </TextValidator>
            </Grid>
            <Grid item xs={4}>
              <TextValidator
                  name="applyDay"
                  label="日"
                  value={(() => {
                    if (!basicInfo || !basicInfo.applyDate || basicInfo.applyDate.length < 8) return '';
                    return parseInt(basicInfo.applyDate.substring(6, 8)).toString() || '';
                  })()}
                  onChange={(e) => {
                    const day = e.target.value;
                    const currentYear = (basicInfo.applyDate && basicInfo.applyDate.length >= 8) ? basicInfo.applyDate.substring(0, 4) : new Date().getFullYear().toString();
                    const currentMonth = (basicInfo.applyDate && basicInfo.applyDate.length >= 8) ? basicInfo.applyDate.substring(4, 6) : '01';
                    const newDate = currentYear && currentMonth && day ? `${currentYear}${currentMonth.padStart(2, '0')}${day.padStart(2, '0')}` : '';
                    
                    // 驗證日期是否有效
                    if (newDate) {
                      const year = parseInt(currentYear);
                      const month = parseInt(currentMonth);
                      const dayNum = parseInt(day);
                      const testDate = new Date(year, month - 1, dayNum);
                      if (testDate.getFullYear() === year && testDate.getMonth() === month - 1 && testDate.getDate() === dayNum) {
                        setBasicInfo({
                          ...basicInfo,
                          applyDate: newDate,
                        });
                      }
                    } else {
                      setBasicInfo({
                        ...basicInfo,
                        applyDate: '',
                      });
                    }
                  }}
                  fullWidth
                  select={true}
                  variant="outlined"
                  validators={["required"]}
                  errorMessages={["請選擇日期"]}
              >
                {[...Array(31)].map((_, index) => {
                  const day = index + 1;
                  return (
                    <MenuItem value={day.toString()} key={day}>
                      {day}日
                    </MenuItem>
                  );
                })}
              </TextValidator>
            </Grid>
            <Grid item xs={12}>
              <TextValidator
                  label="發證地點(必填)"
                  value={basicInfo.applySite}
                  onChange={(e) => {
                    setBasicInfo({
                      ...basicInfo,
                      applySite: e.target.value,
                    });
                  }}
                  fullWidth
                  validators={["required"]}
                  errorMessages={["請選擇發證地點"]}
                  select={true}
                  variant="outlined"
              >
                {codeApplySiteList.map((row) => (
                    <MenuItem value={row.code} key={row.code}>
                      {row.name}
                    </MenuItem>
                ))}
              </TextValidator>
            </Grid>
            <Grid item xs={12}>
              <TextValidator
                  label="領補換類別(必填)"
                  value={basicInfo.applyReason}
                  onChange={(e) => {
                    setBasicInfo({
                      ...basicInfo,
                      applyReason: e.target.value,
                    });
                  }}
                  fullWidth
                  validators={["required"]}
                  errorMessages={["請選擇領補換類別"]}
                  select={true}
                  variant="outlined"
              >
                {codeApplyReasonList.map((row) => (
                    <MenuItem value={row.code} key={row.code}>
                      {row.name}
                    </MenuItem>
                ))}
              </TextValidator>
            </Grid>
          </>
      )}
      { checkPlanState(plan) !== 1 && (
          <>
            <Grid item xs={12}>
              <TextValidator
                  validators={["required", "isDate", "isAgeEnough"]}
                  errorMessages={[
                    "請輸入西元生日",
                    "請輸入正確格式",
                    "很抱歉你的年齡不符合貸款限制",
                  ]}
                  variant="outlined"
                  type="number"
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                  // inputProps={{ max: format(new Date(), "yyyy-MM-dd") }}
                  label="西元生日(必填)"
                  placeholder="YYYYMMDD"
                  disabled={disabledBirthDate}
                  value={basicInfo.birthDate}
                  onChange={(e) => {
                    if (e.target.value.length < 9) {
                      setBasicInfo({ ...basicInfo, birthDate: e.target.value });
                    }
                  }}
              />
            </Grid>
          </>
      )}
      {(userType === "guarantor" && checkPlanState(plan) !== 1) && (
        <>
          <Grid item xs={12}>
            <TextValidator
              value={guaranteeInfo.userSubType}
              label="案件身分別(必填)"
              fullWidth
              validators={["required"]}
              errorMessages={["請選擇案件身分別"]}
              select={true}
              variant="outlined"
              onChange={(e) =>
                setGuaranteeInfo({
                  ...guaranteeInfo,
                  userSubType: e.target.value,
                })
              }
            >
              {userSubTypeList.map((row) => (
                <MenuItem value={row.code} key={row.code}>
                  {row.name}
                </MenuItem>
              ))}
            </TextValidator>
          </Grid>
          {(guaranteeInfo.userSubType === "N" ||
            guaranteeInfo.userSubType === "G") && (
            <Grid item xs={12}>
              <TextValidator
                value={guaranteeInfo.guarantyReasonCode}
                label="本案提徵保證人原因(必填)"
                fullWidth
                validators={["required"]}
                errorMessages={["請選擇本案提徵保證人原因"]}
                select={true}
                variant="outlined"
                onChange={(e) =>
                  setGuaranteeInfo({
                    ...guaranteeInfo,
                    guarantyReasonCode: e.target.value,
                  })
                }
              >
                {guarantyReasonList.map((row) => (
                  <MenuItem value={row.code} key={row.code}>
                    {row.name}
                  </MenuItem>
                ))}
              </TextValidator>
            </Grid>
          )}
          {guaranteeInfo.guarantyReasonCode === "99" && (
            <Grid item xs={12}>
              <TextValidator
                validators={["required"]}
                errorMessages={["請輸入其他保證原因"]}
                id="otherGurantyReason"
                name="otherGurantyReason"
                label="其他保證原因(必填)"
                fullWidth
                variant="outlined"
                value={guaranteeInfo.otherGuarantyReason}
                onChange={(e) =>
                  setGuaranteeInfo({
                    ...guaranteeInfo,
                    otherGuarantyReason: e.target.value,
                  })
                }
              />
            </Grid>
          )}
        </>
      )}
      {(userType !== "borrower" && checkPlanState(plan) !== 1) && (
        <>
          <Grid item xs={12}>
            <TextValidator
              value={guaranteeInfo.relationBorrowerType}
              label="本人為借款人之(必填)"
              fullWidth
              variant="outlined"
              validators={["required"]}
              errorMessages={["請選擇本人為借款人之"]}
              select={true}
              onChange={(e) =>
                setGuaranteeInfo({
                  ...guaranteeInfo,
                  relationBorrowerType: e.target.value,
                })
              }
            >
              {relationBorrowerTypeList.map((row) => (
                <MenuItem value={row.code} key={row.code}>
                  {row.name}
                </MenuItem>
              ))}
            </TextValidator>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="body2">是否與借款人同住</Typography>
            <FormControl component="fieldset">
              <RadioGroup
                aria-label="isCohabiting"
                name="isCohabiting"
                value={guaranteeInfo.isCohabiting}
                onChange={(e) => {
                  let isTrueSet = e.target.value === "true";
                  setGuaranteeInfo({
                    ...guaranteeInfo,
                    isCohabiting: isTrueSet,
                  });
                }}
                row
              >
                <FormControlLabel
                  value={true}
                  control={<Radio color="default" />}
                  label="是"
                  labelPlacement="end"
                />
                <FormControlLabel
                  value={false}
                  control={<Radio color="default" />}
                  label="否"
                  labelPlacement="end"
                />
              </RadioGroup>
            </FormControl>
          </Grid>
        </>
      )}
      { checkPlanState(plan) !== 1 && (
          <>
            <Grid item xs={12}>
              <TextValidator
                  value={basicInfo.educationLevelCode}
                  label="最高學歷(必填)"
                  fullWidth
                  validators={["required"]}
                  errorMessages={["請選擇最高學歷"]}
                  select={true}
                  variant="outlined"
                  onChange={(e) =>
                      setBasicInfo({
                        ...basicInfo,
                        educationLevelCode: e.target.value,
                      })
                  }
              >
                {educationLevelList.map((row) => (
                    <MenuItem value={row.code} key={row.code}>
                      {row.name}
                    </MenuItem>
                ))}
              </TextValidator>
            </Grid>
            <Grid item xs={12}>
              <TextValidator
                  value={basicInfo.marriageStatusCode}
                  label="婚姻狀態(必填)"
                  fullWidth
                  variant="outlined"
                  validators={["required"]}
                  errorMessages={["請選擇婚姻狀態"]}
                  select={true}
                  onChange={(e) =>
                      setBasicInfo({ ...basicInfo, marriageStatusCode: e.target.value })
                  }
              >
                {marriageStatusList.map((row) => (
                    <MenuItem value={row.code} key={row.code}>
                      {row.name}
                    </MenuItem>
                ))}
              </TextValidator>
            </Grid>
            <Grid item xs={12}>
              <TextValidator
                  validators={["required", "isNumber", "maxNumber:99"]}
                  errorMessages={["請輸入子女人數", "請輸入數字", "字數超過長度限制"]}
                  id="childrenCount"
                  name="childrenCount"
                  label="子女人數(必填，若無請填寫0)"
                  fullWidth
                  type="number"
                  variant="outlined"
                  value={basicInfo.childrenCount}
                  onChange={(e) => {
                    if (e.target.value.length < 100) {
                      setBasicInfo({ ...basicInfo, childrenCount: e.target.value });
                    }
                  }}
              />
              {/* <Typography variant="body2" className={classes.color666}>
    若無請填寫0
  </Typography>  */}
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2">是否有多重國籍</Typography>
              <FormControl component="fieldset">
                <RadioGroup
                    aria-label="position"
                    name="position"
                    value={basicInfo.hasOtherNationality}
                    onChange={(e) => {
                      let isTrueSet = e.target.value === "true";
                      setBasicInfo({ ...basicInfo, hasOtherNationality: isTrueSet });
                    }}
                    row
                >
                  <FormControlLabel
                      value={true}
                      control={<Radio color="default" />}
                      label="有"
                      labelPlacement="end"
                  />
                  <FormControlLabel
                      value={false}
                      control={<Radio color="default" />}
                      label="無"
                      labelPlacement="end"
                  />
                </RadioGroup>
              </FormControl>
            </Grid>
            {basicInfo.hasOtherNationality && (
                <Grid item xs={12}>
                  <TextValidator
                      value={basicInfo.nationalityCode}
                      label="其他國籍(必填)"
                      fullWidth
                      variant="outlined"
                      validators={["required"]}
                      errorMessages={["請選擇其他國籍"]}
                      select={true}
                      onChange={(e) =>
                          setBasicInfo({ ...basicInfo, nationalityCode: e.target.value })
                      }
                  >
                    {nationalityList.map((row) => (
                        <MenuItem value={row.code} key={row.code}>
                          {row.name}
                        </MenuItem>
                    ))}
                  </TextValidator>
                </Grid>
            )}
          </>
      )}
    </>
  );
});

//聯絡資料畫面結構
const ContactInfo = React.memo(function ContactInfo(props) {
  const {
    classes,
    contactInfo,
    setContactInfo,
    fetchSubData,
    cityList,
    townList,
    disabledMobileNumber,
    branchTownList,
    branchCityList,
    branchList,
    mailingTownList,
    userType,
    identityType,
    setMailingTownList,
    setErrorResp,
    bankTypeCity,
    identities,
    applyBranchList,
    isYouthsLoan,
    branchListForYouthsLoan,
    plan,
    checkPlanState,
    presetData,
    loanType,
  } = props;
  const [residenceStatusList, setResidenceStatusList] = useState([]);
  const [serviceAssociateDeptList, setServiceAssociateDeptList] = useState([]);
  const [isLoanBank, setIsLoanBank] = useState(true);
  const [refresh, setRefresh] = useState(false);
  const [empIdChanged, setEmpIdChanged] = useState(false);
  const [serviceAssociateBranchName, setServiceAssociateBranchName] = useState("");
  const [serviceAssociateBranchIsLoading, setServiceAssociateBranchIsLoading] = useState(false);

  // console.log("ContactInfo render");
  useEffect(() => {
    fetchData();
  }, []);

  useEffect(()=>{
    doRefresh();
  }, [contactInfo.serviceAssociateDeptCode]);
  useEffect(()=>{
    doRefresh();
  }, [contactInfo.serviceAssociate]);
  useEffect(() => {
    refresh && setTimeout(() => setRefresh(false));
  }, [refresh]);
  useEffect(() => {
    serviceAssociateBranchIsLoading && setTimeout(() => setServiceAssociateBranchIsLoading(false), 10000);
  }, [serviceAssociateBranchIsLoading]);

  const doRefresh = () => {
    // console.log("do refresh");
    setRefresh(true);
  };

  const associateBranchRefresh = () => {
    setServiceAssociateBranchIsLoading(true);
  }
  
  const associateBranchRefreshDone = () => {
    setServiceAssociateBranchIsLoading(false);
  }

  const fetchData = async () => {
    try {
      associateBranchRefresh();

      let [
        residenceStatusRsp,
        serviceAssociateDeptRsp,
        serviceAssociateBranchRsp
      ] = await Promise.all([
        await getCommonCode("residence-status"),
        await getCommonCode("service-associate-dept"),
        await getBranchList(presetData.bno, false)
      ]);
      if (
        residenceStatusRsp.data.stat === "ok" &&
        serviceAssociateDeptRsp.data.stat === "ok" &&
        serviceAssociateBranchRsp.data.stat === "ok"
      ) {
        setResidenceStatusList(residenceStatusRsp.data.result);
        setServiceAssociateDeptList(serviceAssociateDeptRsp.data.result);
        setServiceAssociateBranchName(serviceAssociateBranchRsp.data.result[0]?.name);
      } else {
        let responses = [];
        residenceStatusRsp.data.stat === "error" &&
          responses.push(residenceStatusRsp.data);
        serviceAssociateDeptRsp.data.stat === "error" &&
          responses.push(serviceAssociateDeptRsp.data);
        serviceAssociateBranchRsp.data.stat === "error" &&
          responses.push(serviceAssociateBranchRsp.data)
        setErrorResp(responses);
      }
    } catch (error) {
      setErrorResp([]);
    } finally {
      associateBranchRefreshDone();
    }
    
    setContactInfo({
      ...contactInfo,
      serviceAssociateDeptCode: (presetData.serviceAssociateDeptCode!==null && presetData.serviceAssociateDeptCode!=="") ? presetData.serviceAssociateDeptCode : contactInfo.serviceAssociateDeptCode,
      serviceAssociate: (presetData.serviceAssociate!==null && presetData.serviceAssociate!=="") ? presetData.serviceAssociate : contactInfo.serviceAssociate,
      serviceAssociateBranchCode: (presetData.bno!==null && presetData.bno!=="") ? presetData.bno : contactInfo.serviceAssociateBranchCode,
    });
    setServiceAssociateBranchName(contactInfo.serviceAssociateBranchName);
    console.log("推薦單位、推薦人員、推薦分行資料已設定");
    doRefresh();
  };

  const handleMailingAddressSameToHomeChange = async (value) => {
    let isTrueSet = value === "true";

    if (isTrueSet === true) {
      setContactInfo({
        ...contactInfo,
        mailingAddressAlley: contactInfo.homeAddressAlley,
        mailingAddressCityCode: contactInfo.homeAddressCityCode,
        mailingAddressFloor: contactInfo.homeAddressFloor,
        mailingAddressLane: contactInfo.homeAddressLane,
        mailingAddressNeighborhood: contactInfo.homeAddressNeighborhood,
        mailingAddressNo: contactInfo.homeAddressNo,
        mailingAddressRoom: contactInfo.homeAddressRoom,
        mailingAddressSection: contactInfo.homeAddressSection,
        mailingAddressStreet: contactInfo.homeAddressStreet,
        mailingAddressTownCode: contactInfo.homeAddressTownCode,
        mailingAddressTownName: contactInfo.homeAddressTownName,
        mailingAddressVillage: contactInfo.homeAddressVillage,
        mailingAddressSameToHome: isTrueSet,
      });
    } else {
      setMailingTownList({ list: [], isLoading: false });
      setContactInfo({
        ...contactInfo,
        mailingAddressAlley: "",
        mailingAddressCityCode: "",
        mailingAddressFloor: "",
        mailingAddressLane: "",
        mailingAddressNeighborhood: "",
        mailingAddressNo: "",
        mailingAddressRoom: "",
        mailingAddressSection: "",
        mailingAddressStreet: "",
        mailingAddressTownCode: "",
        mailingAddressTownName: "",
        mailingAddressVillage: "",
        mailingAddressSameToHome: isTrueSet,
      });
    }
  };

  //若不是既有貸款戶或行員，"信貸"申請強制指定承辦銀行為大安分行且於下方隱藏選擇分行欄位
  useEffect(()=>{
    //console.log("目前的branchBankCode : ", contactInfo.branchBankCode);
    if (contactInfo.branchBankCode === "" || contactInfo.branchBankCode === undefined || contactInfo.branchBankCode ===null) {
      if (loanType === "personalloan") {
        setContactInfo({
          ...contactInfo,
          branchBankCode: "20",
          branchBankAddress: "台北市大安區信義路3段182號",
        });
      }
    }
  },[contactInfo]);

  const modifyServiceAssociateBranch = async (empId) => {
    try {
      associateBranchRefresh();
      // bno 優先
      if(loanType === "personalloan" && !presetData.bno) {
        let branchRsp = await getBranchByEmpId(empId);
        if (branchRsp.data.stat === "ok" ) {
          setContactInfo({
            ...contactInfo,
            serviceAssociate: empId,
            serviceAssociateBranchCode: branchRsp.data.result.code,
          });
          
          setServiceAssociateBranchName(branchRsp.data.result.name);
        } else {
          let responses = [];
          branchRsp.data.stat === "error" &&
            responses.push(branchRsp.data);
          setErrorResp(responses);
        }
      }
    } catch (error) {
      // setErrorResp([]);
    } finally {
      associateBranchRefreshDone();
      setEmpIdChanged(false);
    }
  };

  return (
    <>
      <Grid item xs={12}>
        <Typography variant="h6">聯絡資訊</Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="body2">戶籍地址</Typography>
      </Grid>
      <Grid item xs={6}>
        <TextValidator
          value={contactInfo.homeAddressCityCode}
          label="縣市(必填)"
          validators={["required"]}
          errorMessages={["請選擇縣市"]}
          fullWidth
          select={true}
          variant="outlined"
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressCityCode: e.target.value,
              homeAddressTownCode: "",
            });
            fetchSubData(e.target.value, "city");
          }}
        >
          {cityList.map((row) => (
            <MenuItem value={row.code} key={row.code}>
              {row.name}
            </MenuItem>
          ))}
        </TextValidator>
      </Grid>
      <Grid item xs={6}>
        <TextValidator
          value={contactInfo.homeAddressTownCode}
          disabled={townList.list === null}
          label="鄉鎮(必填)"
          fullWidth
          InputProps={
            townList.isLoading ? {
              endAdornment: (
                <InputAdornment position="end">
                  <CircularProgress />
                </InputAdornment>
              ),
            } : undefined
          }
          select={townList.list !== null}
          validators={["required"]}
          errorMessages={["請選擇鄉鎮"]}
          variant="outlined"
          onChange={(e) =>
            setContactInfo({
              ...contactInfo,
              homeAddressTownCode: e.target.value,
            })
          }
        >
          {townList.list !== null &&
            townList.list.map((row) => (
              <MenuItem value={row.code} key={row.code}>
                {row.name}
              </MenuItem>
            ))}
        </TextValidator>
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="homeAddressVillage"
          name="homeAddressVillage"
          validators={["maxStringLength:6"]}
          errorMessages={["字數超過長度限制"]}
          label="村里"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 6,
          }}
          value={contactInfo.homeAddressVillage}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressVillage: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={3}>
        <TextValidator
          id="homeAddressNeighborhood"
          name="homeAddressNeighborhood"
          validators={["maxStringLength:6"]}
          inputProps={{
            maxLength: 6,
          }}
          errorMessages={["字數超過長度限制"]}
          label="鄰"
          fullWidth
          variant="outlined"
          value={contactInfo.homeAddressNeighborhood}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressNeighborhood: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={5}>
        <TextValidator
          id="homeAddressStreet"
          name="homeAddressStreet"
          validators={["required", "maxStringLength:10", "isNotBlank"]}
          errorMessages={["請輸入街道", "字數超過長度限制", "請輸入正確格式"]}
          inputProps={{
            maxLength: 10,
          }}
          label="街道(必填)"
          placeholder="街道(必填)"
          fullWidth
          variant="outlined"
          value={contactInfo.homeAddressStreet}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressStreet: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="homeAddressSection"
          name="homeAddressSection"
          validators={["isNumber", "maxNumber:99999"]}
          errorMessages={["請輸入數字", "字數超過長度限制"]}
          label="段"
          fullWidth
          type="number"
          variant="outlined"
          value={contactInfo.homeAddressSection}
          onChange={(e) => {
            if (e.target.value.length < 100) {
              setContactInfo({
                ...contactInfo,
                homeAddressSection: e.target.value,
              });
            }
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="homeAddressLane"
          name="homeAddressLane"
          validators={["maxStringLength:6"]}
          errorMessages={["字數超過長度限制"]}
          label="巷"
          fullWidth
          inputProps={{
            maxLength: 6,
          }}
          variant="outlined"
          value={contactInfo.homeAddressLane}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressLane: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="homeAddressAlley"
          name="homeAddressAlley"
          validators={["maxStringLength:6"]}
          inputProps={{
            maxLength: 6,
          }}
          errorMessages={["字數超過長度限制"]}
          label="弄"
          fullWidth
          variant="outlined"
          value={contactInfo.homeAddressAlley}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressAlley: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="homeAddressNo"
          name="homeAddressNo"
          validators={["isRegExpOrNumber", "maxStringLength:10", "required", "isNotBlank"]}
          errorMessages={["請輸入數字與符號", "字數超過長度限制", "請輸入號", "請輸入正確格式"]}
          label="號(必填)"
          placeholder="號(必填)"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 10,
          }}
          value={contactInfo.homeAddressNo}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressNo: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="homeAddressFloor"
          name="homeAddressFloor"
          validators={["isNumericOrAlphabet", "maxStringLength:5"]}
          errorMessages={["請輸入數字或英文", "字數超過長度限制"]}
          label="樓"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 5,
          }}
          value={contactInfo.homeAddressFloor}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              homeAddressFloor: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="homeAddressRoom"
          name="homeAddressRoom"
          validators={["isNumber", "maxNumber:99999"]}
          errorMessages={["請輸入數字", "字數超過長度限制"]}
          label="之"
          fullWidth
          type="number"
          variant="outlined"
          value={contactInfo.homeAddressRoom}
          onChange={(e) => {
            if (e.target.value.length < 100) {
              setContactInfo({
                ...contactInfo,
                homeAddressRoom: e.target.value,
              });
            }
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="body2">通訊地址</Typography>
        <FormControl component="fieldset">
          <RadioGroup
            aria-label="position"
            name="position"
            value={contactInfo.mailingAddressSameToHome}
            onChange={(e) =>
              handleMailingAddressSameToHomeChange(e.target.value)
            }
            row
          >
            <FormControlLabel
              value={true}
              control={<Radio color="default" />}
              label="同戶籍地址"
              labelPlacement="end"
            />
            <FormControlLabel
              value={false}
              control={<Radio color="default" />}
              label="另列如下"
              labelPlacement="end"
            />
          </RadioGroup>
        </FormControl>
      </Grid>
      {contactInfo.mailingAddressSameToHome === false && (
        <>
          <Grid item xs={6}>
            <TextValidator
              value={contactInfo.mailingAddressCityCode}
              label="縣市(必填)"
              fullWidth
              variant="outlined"
              select={true}
              validators={["required"]}
              errorMessages={["請選擇縣市"]}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressCityCode: e.target.value,
                  mailingAddressTownCode: "",
                });
                fetchSubData(e.target.value, "mailing-city");
              }}
            >
              {cityList.map((row) => (
                <MenuItem value={row.code} key={row.code}>
                  {row.name}
                </MenuItem>
              ))}
            </TextValidator>
          </Grid>
          <Grid item xs={6}>
            <TextValidator
              disabled={mailingTownList.list === null}
              validators={["required"]}
              errorMessages={["請選擇鄉鎮"]}
              label="鄉鎮(必填)"
              fullWidth
              InputProps={
                mailingTownList.isLoading ? {
                  endAdornment: (
                    <InputAdornment position="end">
                      <CircularProgress />
                    </InputAdornment>
                  ),
                } : undefined
              }
              select={mailingTownList.list !== null}
              variant="outlined"
              value={contactInfo.mailingAddressTownCode}
              onChange={(e) =>
                setContactInfo({
                  ...contactInfo,
                  mailingAddressTownCode: e.target.value,
                })
              }
            >
              {mailingTownList.list !== null &&
                mailingTownList.list.map((row) => (
                  <MenuItem value={row.code} key={row.code}>
                    {row.name}
                  </MenuItem>
                ))}
            </TextValidator>
          </Grid>
          <Grid item xs={4}>
            <TextValidator
              id="mailingAddressVillage"
              name="mailingAddressVillage"
              validators={["maxStringLength:6"]}
              errorMessages={["字數超過長度限制"]}
              label="村里"
              inputProps={{
                maxLength: 6,
              }}
              fullWidth
              variant="outlined"
              value={contactInfo.mailingAddressVillage}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressVillage: e.target.value,
                });
              }}
            />
          </Grid>
          <Grid item xs={3}>
            <TextValidator
              id="mailingAddressNeighborhood"
              name="mailingAddressNeighborhood"
              validators={["maxStringLength:6"]}
              inputProps={{
                maxLength: 6,
              }}
              errorMessages={["字數超過長度限制"]}
              label="鄰"
              fullWidth
              variant="outlined"
              value={contactInfo.mailingAddressNeighborhood}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressNeighborhood: e.target.value,
                });
              }}
            />
          </Grid>
          <Grid item xs={5}>
            <TextValidator
              id="mailingAddressStreet"
              name="mailingAddressStreet"
              validators={["required", "maxStringLength:10", "isNotBlank"]}
              errorMessages={["請輸入街道", "字數超過長度限制", "請輸入正確格式"]}
              inputProps={{
                maxLength: 10,
              }}
              label="街道(必填)"
              placeholder="街道(必填)"
              fullWidth
              variant="outlined"
              value={contactInfo.mailingAddressStreet}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressStreet: e.target.value,
                });
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <TextValidator
              id="mailingAddressSection"
              name="mailingAddressSection"
              validators={["isNumber", "maxNumber:99999"]}
              errorMessages={["請輸入數字", "字數超過長度限制"]}
              label="段"
              fullWidth
              type="number"
              variant="outlined"
              value={contactInfo.mailingAddressSection}
              onChange={(e) => {
                if (e.target.value.length < 100) {
                  setContactInfo({
                    ...contactInfo,
                    mailingAddressSection: e.target.value,
                  });
                }
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <TextValidator
              id="mailingAddressLane"
              name="mailingAddressLane"
              validators={["maxStringLength:6"]}
              inputProps={{
                maxLength: 6,
              }}
              errorMessages={["字數超過長度限制"]}
              label="巷"
              fullWidth
              variant="outlined"
              value={contactInfo.mailingAddressLane}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressLane: e.target.value,
                });
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <TextValidator
              id="mailingAddressAlley"
              name="mailingAddressAlley"
              validators={["maxStringLength:6"]}
              errorMessages={["字數超過長度限制"]}
              label="弄"
              fullWidth
              inputProps={{
                maxLength: 6,
              }}
              variant="outlined"
              value={contactInfo.mailingAddressAlley}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressAlley: e.target.value,
                });
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <TextValidator
              id="mailingAddressNo"
              name="mailingAddressNo"
              validators={[
                "matchRegexp:[0-9 -]+",
                "maxStringLength:10",
                "required",
                "isNotBlank",
              ]}
              errorMessages={["請輸入數字", "字數超過長度限制", "請輸入號", "請輸入正確格式"]}
              label="號(必填)"
              placeholder="號(必填)"
              fullWidth
              variant="outlined"
              inputProps={{
                maxLength: 10,
              }}
              value={contactInfo.mailingAddressNo}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressNo: e.target.value,
                });
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <TextValidator
              id="mailingAddressFloor"
              name="mailingAddressFloor"
              validators={["isNumericOrAlphabet", "maxStringLength:5"]}
              errorMessages={["請輸入數字或英文", "字數超過長度限制"]}
              label="樓"
              fullWidth
              inputProps={{
                maxLength: 2,
              }}
              variant="outlined"
              value={contactInfo.mailingAddressFloor}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  mailingAddressFloor: e.target.value,
                });
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <TextValidator
              id="mailingAddressRoom"
              name="mailingAddressRoom"
              validators={["isNumber", "maxNumber:99999"]}
              errorMessages={["請輸入數字", "字數超過長度限制"]}
              label="之"
              fullWidth
              type="number"
              variant="outlined"
              value={contactInfo.mailingAddressRoom}
              onChange={(e) => {
                if (e.target.value.length < 100) {
                  setContactInfo({
                    ...contactInfo,
                    mailingAddressRoom: e.target.value,
                  });
                }
              }}
            />
          </Grid>
        </>
      )}
      <Grid item xs={12}>
        <TextValidator
          label="現住房屋持有狀態(必填)"
          fullWidth
          validators={["required"]}
          errorMessages={["請選擇現住房屋持有狀態"]}
          variant="outlined"
          value={contactInfo.residenceStatusCode}
          onChange={(e) =>
            setContactInfo({
              ...contactInfo,
              residenceStatusCode: e.target.value,
            })
          }
          select={true}
        >
          {residenceStatusList.map((row) => (
            <MenuItem value={row.code} key={row.code}>
              {row.name}
            </MenuItem>
          ))}
        </TextValidator>
      </Grid>
      {contactInfo.residenceStatusCode === "6" && (
        <Grid item xs={12}>
          <TextValidator
            id="rent"
            name="rent"
            label="租金(必填)"
            validators={["required", "isNumber", "maxNumber:999999"]}
            errorMessages={["請輸入租金", "請輸入數字", "字數超過長度限制"]}
            InputProps={{
              endAdornment: <InputAdornment position="end">元</InputAdornment>,
            }}
            fullWidth
            variant="outlined"
            type="number"
            value={contactInfo.rent}
            onChange={(e) => {
              if (e.target.value.length < 7) {
                setContactInfo({
                  ...contactInfo,
                  rent: e.target.value,
                });
              }
            }}
          />
        </Grid>
      )}
      {/* <Grid item xs={12}>
        <TextValidator
          value={contactInfo.houseStatusCode}
          select={true}
          label="不動產狀況(必填)"
          fullWidth
          validators={["required"]}
          errorMessages={["請選擇不動產狀況"]}
          variant="outlined"
          onChange={(e) =>
            setContactInfo({
              ...contactInfo,
              houseStatusCode: e.target.value,
            })
          }
        >
          {houseStatusList.map((row) => (
            <MenuItem value={row.code} key={row.code}>
              {row.name}
            </MenuItem>
          ))}
        </TextValidator>
      </Grid> */}
      <Grid item xs={12}>
        <Typography variant="body2">住宅電話</Typography>
        <FormControl component="fieldset">
          <RadioGroup
            aria-label="position"
            name="position"
            value={contactInfo.hasHomePhone}
            onChange={(e) => {
              let isTrueSet = e.target.value === "true";
              setContactInfo({
                ...contactInfo,
                hasHomePhone: isTrueSet,
              });
            }}
            row
          >
            <FormControlLabel
              value={true}
              control={<Radio color="default" />}
              label="有"
              labelPlacement="end"
            />
            <FormControlLabel
              value={false}
              control={<Radio color="default" />}
              label="無"
              labelPlacement="end"
            />
          </RadioGroup>
        </FormControl>
      </Grid>
      {contactInfo.hasHomePhone && (
        <>
          <Grid item xs={5}>
            <TextValidator
              id="homePhoneCode"
              name="homePhoneCode"
              label="區碼(必填)"
              validators={[
                "required",
                "isNumber",
                "maxStringLength:4",
                "minStringLength:2",
              ]}
              errorMessages={[
                "請輸入區碼",
                "請輸入數字",
                "字數超過長度限制",
                "請輸入正確格式",
              ]}
              fullWidth
              type="tel"
              variant="outlined"
              value={contactInfo.homePhoneCode}
              onChange={(e) => {
                if (e.target.value.length < 5) {
                  setContactInfo({
                    ...contactInfo,
                    homePhoneCode: e.target.value,
                  });
                }
              }}
            />
          </Grid>
          <Grid item xs={7}>
            <TextValidator
              id="homePhoneNumber"
              name="homePhoneNumber"
              label="電話號碼(必填)"
              validators={[
                "required",
                "isNumber",
                "maxStringLength:8",
                "minStringLength:5",
              ]}
              errorMessages={[
                "請輸入電話號碼",
                "請輸入數字",
                "字數超過長度限制",
                "請輸入正確格式",
              ]}
              fullWidth
              type="tel"
              inputProps={{
                maxLength: 8
              }}
              variant="outlined"
              value={contactInfo.homePhoneNumber}
              onChange={(e) => {
                setContactInfo({
                  ...contactInfo,
                  homePhoneNumber: e.target.value,
                });
              }}
            />
          </Grid>
        </>
      )}
      <Grid item xs={12}>
        <TextValidator
          validators={[
            "required",
            "isNumber",
            "maxStringLength:10",
            "minStringLength:10",
          ]}
          errorMessages={[
            "請輸入手機號碼",
            "請輸入數字",
            "字數超過長度限制",
            "請輸入正確格式",
          ]}
          id="mobileNumber"
          name="mobileNumber"
          label="手機號碼(必填)"
          fullWidth
          type="tel"
          disabled={disabledMobileNumber}
          variant="outlined"
          value={contactInfo.mobileNumber}
          onChange={(e) => {
            if (e.target.value.length < 11) {
              setContactInfo({
                ...contactInfo,
                mobileNumber: e.target.value,
              });
            }
          }}
        />
        {identityType === "skip" && (
          <Typography variant="body2" className={classes.color666}>
            此手機供未來線上貸款申請書調閱使用
          </Typography>
        )}
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          validators={["required", 'isEmail' , "maxStringLength:50", 'isNotChinese']} //removed ""  //matchRegexp:^[a-zA-Z0-9@_\-.]+$
          errorMessages={[
            "請輸入郵件信箱",
            "請輸入正確格式",
            "字數超過長度限制",
            "不可輸入中文"
          ]}
          id="email"
          name="email"
          label="Email(必填)"
          placeholder="Email(必填)"
          fullWidth
          autoComplete="email"
          variant="outlined"
          value={contactInfo.email}
          inputProps={{
            maxLength: 50,
          }}
          onChange={(e) => {
            setContactInfo({
              ...contactInfo,
              email: e.target.value,
            });
          }}
        />
      </Grid>
      {(userType === "borrower" && checkPlanState(plan) === 0 ) 
      && ((identities.includes("loanee") || identities.includes("employee")) || loanType === "houseloan" )
      && (
        <>
          {(!(loanType === "personalloan" && userType === "borrower")) &&
            <>
              <Grid item xs={12}>
                <Typography variant="body2">方便往來分行</Typography>
                {identities.includes("loanee") && !isYouthsLoan && (
                  <FormControl component="fieldset">
                    <RadioGroup
                      aria-label="isLoanBank"
                      name="isLoanBank"
                      value={isLoanBank}
                      onChange={(e) => {
                        let isTrueSet = e.target.value === "true";
                        setIsLoanBank(isTrueSet);
                      }}
                      row
                    >
                      <FormControlLabel
                        value={true}
                        control={<Radio color="default" />}
                        label="貸款往來分行"
                        labelPlacement="end"
                      />
                      <FormControlLabel
                        value={false}
                        control={<Radio color="default" />}
                        label="其他"
                        labelPlacement="end"
                      />
                    </RadioGroup>
                  </FormControl>
                )}
              </Grid>
              {identities.includes("loanee") && isLoanBank && !isYouthsLoan && (!(loanType === "personalloan" && userType === "borrower")) &&
                (<Grid item xs={12}>
                  <TextValidator
                    value={contactInfo.branchBankCode}
                    label="分行(必填)"
                    fullWidth
                    select={true}
                    validators={["required"]}
                    errorMessages={["請選擇分行"]}
                    variant="outlined"
                    disabled={(checkPlanState(plan)===2)}
                    onChange={(e) => {
                      setContactInfo({
                        ...contactInfo,
                        branchBankCode: e.target.value,
                        branchBankAddress: branchList.list ? 
                          branchList.list.find(item => item.code === e.target.value)?.address || "" : "",
                      });
                    }}
                  >
                    {applyBranchList.map((row) => (
                      <MenuItem
                        value={row.code}
                        key={row.code}
                        address={row.address}
                      >
                        {row.name}
                      </MenuItem>
                    ))}
                  </TextValidator>
                  {contactInfo.branchBankAddress !== "" && (
                    <Typography variant="body2">
                      分行地址：
                      {contactInfo.branchBankAddress}
                    </Typography>
                  )}
                </Grid>
              )}
            </>
          }
          {isYouthsLoan && (!(loanType === "personalloan" && userType === "borrower")) && (
            <Grid item xs={12}>
              <TextValidator
                value={contactInfo.branchBankCode}
                label="分行(必填)"
                fullWidth
                select={true}
                validators={["required"]}
                errorMessages={["請選擇分行"]}
                disabled={true} //分行下拉選單不可改變
                variant="outlined"
                onChange={(e) => {
                  setContactInfo({
                    ...contactInfo,
                    branchBankCode: e.target.value,
                    branchBankAddress: branchList.list ? 
                      branchList.list.find(item => item.code === e.target.value)?.address || "" : "",
                  });
                }}
              >
                {branchListForYouthsLoan.map((row) => (
                  <MenuItem
                    value={row.code}
                    key={row.code}
                    address={row.address}
                  >
                    {row.name}
                  </MenuItem>
                ))}
              </TextValidator>
              {contactInfo.branchBankAddress !== "" && (
                <Typography variant="body2">
                  分行地址：
                  {contactInfo.branchBankAddress}
                </Typography>
              )}
            </Grid>
          )}
          {(!identities.includes("loanee") || !isLoanBank) && loanType !== "personalloan" && !isYouthsLoan && (
            <>
              <Grid item xs={12} sm={6}>
                <TextValidator
                  value={contactInfo.branchBankCityCode}
                  label="縣市(必填)"
                  validators={["required"]}
                  errorMessages={["請選擇縣市"]}
                  fullWidth
                  select={true}
                  variant="outlined"
                  onChange={(e) => {
                    setContactInfo({
                      ...contactInfo,
                      branchBankCode: "",
                      branchBankCityCode: e.target.value,
                      branchBankAddress: "",
                    });
                    // setBranchCity(e.target.value);
                    // let bankType;
                    // if (loanType === "personalloan") {
                    //   bankType = "pl-branch-bank-city";
                    // } else {
                    //   bankType = "hl-branch-bank-city";
                    // }
                    // fetchSubData(e.target.value, bankTypeCity);
                  }}
                >
                  {branchCityList.map((row) => (
                    <MenuItem value={row.code} key={row.code}>
                      {row.name}
                    </MenuItem>
                  ))}
                </TextValidator>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextValidator
                  value={contactInfo.branchBankTownCode}
                  disabled={branchTownList.list === null ? true : false}
                  label="區(必填)"
                  validators={["required"]}
                  errorMessages={["請選擇區"]}
                  fullWidth
                  select={branchTownList.list === null ? false : true}
                  variant="outlined"
                  InputProps={
                    branchTownList.isLoading ? {
                      endAdornment: (
                        <InputAdornment position="end">
                          <CircularProgress />
                        </InputAdornment>
                      ),
                    } : undefined
                  }
                  onChange={(e) => {
                    setContactInfo({
                      ...contactInfo,
                      branchBankTownCode: e.target.value,
                    });
                    // setBranchTown(e.target.value);
                    // fetchBranchList(e.target.value, bankType);
                  }}
                >
                  {branchTownList.list !== null &&
                    branchTownList.list.map((row) => (
                      <MenuItem value={row.code} key={row.code}>
                        {row.name}
                      </MenuItem>
                    ))}
                </TextValidator>
              </Grid>
              {(!(loanType === "personalloan" && userType === "borrower")) &&
                <Grid item xs={12}>
                  <TextValidator
                    value={contactInfo.branchBankCode}
                    disabled={branchList.list === null}
                    label="分行(必填)"
                    fullWidth
                    select={branchList.list !== null}
                    validators={["required"]}
                    errorMessages={["請選擇分行"]}
                    variant="outlined"
                    InputProps={
                      branchList.isLoading ? {
                        endAdornment: (
                          <InputAdornment position="end">
                            <CircularProgress />
                          </InputAdornment>
                        ),
                      } : undefined
                    }
                    onChange={(e) => {
                      setContactInfo({
                        ...contactInfo,
                        branchBankCode: e.target.value,
                        branchBankAddress: branchList.list ? 
                          branchList.list.find(item => item.code === e.target.value)?.address || "" : "",
                      });
                    }}
                  >
                    {branchList.list !== null &&
                      branchList.list.map((row) => (
                        <MenuItem
                          value={row.code}
                          key={row.code}
                          address={row.address}
                        >
                          {row.name}
                        </MenuItem>
                      ))}
                  </TextValidator>
                  {contactInfo.branchBankAddress !== "" && (
                    <Typography variant="body2">
                      分行地址：
                      {contactInfo.branchBankAddress}
                    </Typography>
                  )}
                </Grid>
              }
            </>
          )}
        </>
      )}
      {(userType === "borrower" && checkPlanState(plan)!==2) && (
        <>
          <Grid item xs={6}>
            <TextValidator
              value={contactInfo.serviceAssociateDeptCode}
              label="推薦單位名稱"
              fullWidth
              select={true}
              variant="outlined"
              //defaultValue={null}
              disabled={presetData.lockMKTInfo === "true" || serviceAssociateBranchIsLoading || empIdChanged}
              onChange={(e) => {
                if(presetData.bno) {
                  setContactInfo({
                    ...contactInfo,
                    serviceAssociateDeptCode: e.target.value,
                    serviceAssociate: ""
                  });
                } else {
                  setContactInfo({
                    ...contactInfo,
                    serviceAssociateDeptCode: e.target.value,
                    serviceAssociate: "",
                    serviceAssociateBranchCode: "",
                  });
                }
                setServiceAssociateBranchName("");
                setEmpIdChanged(false);
              }}
            >
              <MenuItem value={null} key={0}>
                請選擇推薦單位
              </MenuItem>
              {serviceAssociateDeptList !== null &&
                serviceAssociateDeptList.map((row) => (
                  <MenuItem value={row.code} key={row.code}>
                    {row.name}
                  </MenuItem>
                ))}
            </TextValidator>
          </Grid>
          <Grid item xs={6}>
            { contactInfo.serviceAssociateDeptCode == 90000 ?
              <TextValidator
                id="serviceAssociate"
                name="serviceAssociate"
                validators={["isNumericOrAlphabet", "maxStringLength:10"]}
                errorMessages={["請輸入英文或數字", "字數超過長度限制"]}
                label="推薦人員編號"
                fullWidth
                variant="outlined"
                inputProps={{
                  maxLength: 10,
                }}
                value={contactInfo.serviceAssociate}
                disabled={presetData.lockMKTInfo === "true" || serviceAssociateBranchIsLoading}
                onChange={(e) => {
                  setContactInfo({
                    ...contactInfo,
                    serviceAssociate: e.target.value ,
                  });
                  setServiceAssociateBranchName("");
                  setEmpIdChanged(true);
                }}
                onBlur={(e) => {
                  console.log("service associate activate!!!!!");
                  setContactInfo({
                    ...contactInfo,
                    serviceAssociate: e.target.value.length < 6 && e.target.value.length > 0 ? String(e.target.value).padStart(6, "0") : e.target.value ,
                  });
                  modifyServiceAssociateBranch(e.target.value.length < 6 && e.target.value.length > 0 ? String(e.target.value).padStart(6, "0") : e.target.value);
                }}
              />
              :
              <TextValidator
                id="serviceAssociate"
                name="serviceAssociate"
                validators={["isNumericOrAlphabet", "maxStringLength:10"]}
                errorMessages={["請輸入英文或數字", "字數超過長度限制"]}
                label="推薦人員編號"
                fullWidth
                variant="outlined"
                inputProps={{
                  maxLength: 10,
                }}
                value={contactInfo.serviceAssociate}
                disabled={presetData.lockMKTInfo === "true"}
                onChange={(e) => {
                  setContactInfo({
                    ...contactInfo,
                    serviceAssociate: e.target.value,
                  });
                }}
              />
            }
          </Grid>
          {(loanType === "personalloan") &&
            <Grid item xs={12}>
              {empIdChanged &&
                <TextValidator
                  id="serviceAssociateBranchName"
                  name="serviceAssociateBranchName"
                  validators={["required"]}
                  errorMessages={["請等待推薦分行載入"]}
                  label="推薦分行"
                  fullWidth
                  variant="outlined"
                  value={serviceAssociateBranchName}
                  disabled={true}
                /> 
              }
              {!empIdChanged &&
                <TextValidator
                  id="serviceAssociateBranchName"
                  name="serviceAssociateBranchName"
                  validators={[]}
                  errorMessages={[]}
                  label="推薦分行"
                  fullWidth
                  variant="outlined"
                  value={serviceAssociateBranchName}
                  disabled={true}
                />
              }
            </Grid>
          }
        </>
      )}
    </>
  );
});

//工作資料畫面結構
const JobInfo = React.memo(function JobInfo(props) {
  const {
    loanType,
    jobInfo,
    setJobInfo,
    jobSubTypeList,
    companyTownList,
    cityList,
    fetchSubData,
    jobTypeList,
    setErrorResp,
    plan,
    checkPlanState,
    presetData,
  } = props;
  const [amountPerMonthList, setAmountPerMonthList] = useState([]);
  const [titleTypeList, setTitleTypeList] = useState([]);

  // console.log("JobInfo render");
  useEffect(() => {
    fetchData();
    if (jobInfo !== null) {
      if (checkPlanState(plan)===3) {
        fetchSubData('06', "job-type");
        fetchSubData('18', "company-city");
      }
      setJobInfo({
        ...jobInfo,
        empNo: window.sessionStorage.getItem("empNo") ? window.sessionStorage.getItem("empNo") : "",
        companyName : (presetData.companyName!==null && presetData.companyName!=="") ? decodeURIComponent(presetData.companyName) : jobInfo.companyName,
        companyTaxNo: (presetData.companyTaxNo!==null && presetData.companyTaxNo!=="") ? presetData.companyTaxNo : jobInfo.companyTaxNo,
        jobType: (checkPlanState(plan)===3) ? '06' : jobInfo.jobType,
        jobSubType: (checkPlanState(plan)===3) ? '15' : jobInfo.jobSubType,
        companyPhoneCode: (checkPlanState(plan)===3) ? '03' : '',
        companyPhoneNumber: (checkPlanState(plan)===3) ? '6668788' : '',
        companyAddressCityCode: (checkPlanState(plan)===3) ? '18' : '',
        companyAddressTownCode: (checkPlanState(plan)===3) ? '1801' : '',
        companyAddressStreet: (checkPlanState(plan)===3) ? '篤行一路' : '',
        companyAddressNo: (checkPlanState(plan)===3) ? '12' : '',
      });
    }
  }, []);

  const fetchData = async () => {
    try {
      let [amountPerMonthRsp, titleTypeRsp] = await Promise.all([
        await getCommonCode("amount-per-month"),
        await getCommonCode("title-type"),
      ]);
      if (
        amountPerMonthRsp.data.stat === "ok" &&
        titleTypeRsp.data.stat === "ok"
      ) {
        setAmountPerMonthList(amountPerMonthRsp.data.result);
        setTitleTypeList(titleTypeRsp.data.result);
      } else {
        let responses = [];
        amountPerMonthRsp.data.stat === "error" &&
          responses.push(amountPerMonthRsp.data);
        titleTypeRsp.data.stat === "error" && responses.push(titleTypeRsp.data);
        setErrorResp(responses);
      }
    } catch (error) {
      setErrorResp([]);
    }
  };

  return (
    <>
      <Grid item xs={12}>
        <Typography variant="h6">職業與收入</Typography>
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          value={jobInfo.jobType}
          validators={["required"]}
          errorMessages={["請選擇職業別"]}
          label="職業別(必填)"
          select={true}
          fullWidth
          variant="outlined"
          //disabled={(checkPlanState(plan)===2)}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              jobType: e.target.value,
              jobSubType: "",
            });
            fetchSubData(e.target.value, "job-type");
          }}
        >
          {jobTypeList.map((row) => (
            <MenuItem value={row.code} key={row.code}>
              {row.name}
            </MenuItem>
          ))}
        </TextValidator>
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          value={jobInfo.jobSubType}
          label="職業別細項(必填)"
          fullWidth
          validators={["required"]}
          errorMessages={["請選擇職業別細項"]}
          variant="outlined"
          onChange={(e) => 
            setJobInfo({
              ...jobInfo,
              jobSubType: e.target.value,
            })
          }
          disabled={jobSubTypeList === null}
          InputProps={
            jobSubTypeList.isLoading ? {
              endAdornment: (
                <InputAdornment position="end">
                  <CircularProgress />
                </InputAdornment>
              ),
            } : undefined
          }
          select={jobSubTypeList.list !== null}
        >
          {jobSubTypeList.list !== null &&
            jobSubTypeList.list.map((row) => (
              <MenuItem value={row.code} key={row.code}>
                {row.name}
              </MenuItem>
            ))}
        </TextValidator>
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          value={jobInfo.amountPerMonthCode}
          // validators={["required"]}
          // errorMessages={["請選擇本行帳戶預期月平均交易金額"]}
          label="本行帳戶預期月平均交易金額(必填)"
          validators={["required"]}
          errorMessages={["請輸入本行帳戶預期月平均交易金額"]}
          select={true}
          fullWidth
          variant="outlined"
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              amountPerMonthCode: e.target.value,
            });
          }}
        >
          {amountPerMonthList.map((row) => (
            <MenuItem value={row.code} key={row.code}>
              {row.name}
            </MenuItem>
          ))}
        </TextValidator>
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          validators={["required", "isRegExp", "maxStringLength:20", "isNotBlank"]}
          errorMessages={[
            "請輸入服務單位名稱",
            "不能輸入特殊符號",
            "字數超過長度限制",
            "請輸入正確格式",
          ]}
          id="companyName"
          name="companyName"
          inputProps={{
            maxLength: 20,
          }}
          label="服務單位名稱(必填)"
          placeholder="服務單位名稱(必填)"
          fullWidth
          variant="outlined"
          value={jobInfo.companyName}
          disabled={(checkPlanState(plan)===2) || presetData.lockWRKInfo === "true"}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyName: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          validators={
            loanType === "personalloan"
              ? ["required", "isNumber", "maxNumber:9999", "minNumber:30"]
              : ["required", "isNumber", "maxNumber:9999"]
          }
          errorMessages={
            loanType === "personalloan"
              ? [
                  "請輸入現職年薪",
                  "請輸入數字",
                  "字數超過長度限制",
                  "申請信用貸款需年薪達30萬元以上",
                ]
              : ["請輸入現職年薪", "請輸入數字", "字數超過長度限制"]
          }
          id="annualIncome"
          name="annualIncome"
          label="現職年薪(必填)"
          fullWidth
          InputProps={{
            endAdornment: <InputAdornment position="end">萬</InputAdornment>,
          }}
          type="number"
          variant="outlined"
          value={jobInfo.annualIncome}
          onChange={(e) => {
            if (e.target.value.length < 5) {
              setJobInfo({
                ...jobInfo,
                annualIncome: e.target.value,
              });
            }
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="body2">年資(必填)</Typography>
      </Grid>
      <Grid item xs={6}>
        <TextValidator
          validators={["required", "isNumber", "maxNumber:99"]}
          errorMessages={["請輸入年份", "請輸入數字", "字數超過長度限制"]}
          id="seniorityYear"
          label="年"
          placeholder="年"
          type="number"
          name="seniorityYear"
          fullWidth
          // InputProps={{
          //   endAdornment: <InputAdornment position="end">年</InputAdornment>,
          // }}
          variant="outlined"
          value={jobInfo.seniorityYear}
          onChange={(e) => {
            if (e.target.value.length < 3) {
              setJobInfo({
                ...jobInfo,
                seniorityYear: e.target.value,
              });
            }
          }}
        />
        {/* <Typography variant="body2" className={classes.color666}>
          同性質工作年資
        </Typography> */}
      </Grid>
      <Grid item xs={6}>
        <TextValidator
          validators={["required", "isNumber", "maxNumber:11", "minNumber:0"]}
          errorMessages={[
            "請輸入月份",
            "請輸入數字",
            "請輸入正確格式",
            "請輸入正確格式",
          ]}
          type="number"
          id="seniorityMonth"
          name="seniorityMonth"
          fullWidth
          label="月"
          placeholder="月"
          // InputProps={{
          //   endAdornment: <InputAdornment position="end">月</InputAdornment>,
          // }}
          variant="outlined"
          value={jobInfo.seniorityMonth}
          onChange={(e) => {
            if (e.target.value.length < 3) {
              setJobInfo({
                ...jobInfo,
                seniorityMonth: e.target.value,
              });
            }
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          value={jobInfo.titleType}
          label="職稱(必填)"
          fullWidth
          validators={["required"]}
          errorMessages={["請選擇職稱"]}
          select={true}
          variant="outlined"
          onChange={(e) =>
            setJobInfo({
              ...jobInfo,
              titleType: e.target.value,
            })
          }
        >
          {titleTypeList.map((row) => (
            <MenuItem value={row.code} key={row.code}>
              {row.name}
            </MenuItem>
          ))}
        </TextValidator>
      </Grid>
      <Grid item xs={12}>
        <TextValidator
          validators={["isTaxNo", "maxStringLength:8"]}
          errorMessages={["請輸入正確格式", "字數超過長度限制"]}
          id="companyTaxNo"
          name="companyTaxNo"
          label="服務單位統一編號"
          fullWidth
          type="number"
          inputProps={{
            maxLength: 8,
          }}
          variant="outlined"
          value={jobInfo.companyTaxNo}
          disabled={(checkPlanState(plan)===2) || presetData.lockWRKInfo === "true"}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyTaxNo: e.target.value,
            });
          }}
        />
      </Grid>
      { checkPlanState(plan)===2 &&
        <Grid item xs={12}>
          <TextValidator
            validators={["required", "maxStringLength:6"]}
            errorMessages={["請輸入員工編號", "字數超過長度限制"]}
            id="empNo"
            name="empNo"
            //label={jobInfo.empNo === "" || jobInfo.empNo === null ? "員工編號(必填)": null}
            label="員工編號(必填)"
            fullWidth
            variant="outlined"
            value={jobInfo.empNo}
            onChange={(e) => {
              setJobInfo({
                ...jobInfo,
                empNo: e.target.value,
              });
              window.sessionStorage.setItem("empNo", e.target.value);
            }}
          />
      </Grid>
      }
      <Grid item xs={12}>
        <Typography variant="body2">服務單位電話</Typography>
      </Grid>
      <Grid item xs={5} sm={3}>
        <TextValidator
          validators={[
            "required",
            "isNumber",
            "maxStringLength:4",
            "minStringLength:2",
          ]}
          errorMessages={[
            "請輸入區碼",
            "請輸入數字",
            "字數超過長度限制",
            "請輸入正確格式",
          ]}
          id="companyPhoneCode"
          name="companyPhoneCode"
          label="區碼(必填)"
          placeholder="區碼(必填)"
          fullWidth
          variant="outlined"
          type="tel"
          value={jobInfo.companyPhoneCode}
          onChange={(e) => {
            if (e.target.value.length < 5) {
              setJobInfo({
                ...jobInfo,
                companyPhoneCode: e.target.value,
              });
            }
          }}
        />
      </Grid>
      <Grid item xs={7} sm={6}>
        <TextValidator
          validators={[
            "required",
            "isNumber",
            "maxStringLength:8",
            "minStringLength:5",
          ]}
          errorMessages={[
            "請輸入電話號碼",
            "請輸入數字",
            "字數超過長度限制",
            "請輸入正確格式",
          ]}
          id="companyPhoneNumber"
          name="companyPhoneNumber"
          label="電話號碼(必填)"
          placeholder="電話號碼(必填)"
          fullWidth
          inputProps={{
            maxLength: 8
          }}
          type="tel"
          variant="outlined"
          value={jobInfo.companyPhoneNumber}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyPhoneNumber: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={12} sm={3}>
        <TextValidator
          id="companyPhoneExt"
          name="companyPhoneExt"
          label="分機"
          fullWidth
          type="number"
          validators={["isNumber", "maxStringLength:10"]}
          errorMessages={["請輸入數字", "字數超過長度限制"]}
          variant="outlined"
          value={jobInfo.companyPhoneExt}
          inputProps={{
            maxLength: 10,
          }}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyPhoneExt: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="body2">服務單位地址</Typography>
      </Grid>
      <Grid item xs={6}>
        <TextValidator
          value={jobInfo.companyAddressCityCode}
          label="縣市(必填)"
          validators={["required"]}
          errorMessages={["請選擇縣市"]}
          fullWidth
          select={true}
          variant="outlined"
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressCityCode: e.target.value,
              companyAddressTownCode: "",
            });
            fetchSubData(e.target.value, "company-city");
          }}
        >
          {cityList.map((row) => (
            <MenuItem value={row.code} key={row.code}>
              {row.name}
            </MenuItem>
          ))}
        </TextValidator>
      </Grid>
      <Grid item xs={6}>
        <TextValidator
          value={jobInfo.companyAddressTownCode}
          disabled={companyTownList.list === null}
          label="鄉鎮(必填)"
          fullWidth
          InputProps={
            companyTownList.isLoading ? {
              endAdornment: (
                <InputAdornment position="end">
                  <CircularProgress />
                </InputAdornment>
              ),
            } : undefined
          }
          select={companyTownList.list !== null}
          validators={["required"]}
          errorMessages={["請選擇鄉鎮"]}
          variant="outlined"
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressTownCode: e.target.value,
            });
          }}
        >
          {companyTownList.list !== null &&
            companyTownList.list.map((row) => (
              <MenuItem value={row.code} key={row.code}>
                {row.name}
              </MenuItem>
            ))}
        </TextValidator>
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="companyAddressVillage"
          name="companyAddressVillage"
          validators={["maxStringLength:6"]}
          errorMessages={["字數超過長度限制"]}
          label="村里"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 6,
          }}
          value={jobInfo.companyAddressVillage}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressVillage: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={3}>
        <TextValidator
          id="companyAddressNeighborhood"
          name="companyAddressNeighborhood"
          validators={["maxStringLength:6"]}
          errorMessages={["字數超過長度限制"]}
          label="鄰"
          inputProps={{
            maxLength: 6,
          }}
          fullWidth
          variant="outlined"
          value={jobInfo.companyAddressNeighborhood}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressNeighborhood: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={5}>
        <TextValidator
          id="companyAddressStreet"
          name="companyAddressStreet"
          validators={["required", "maxStringLength:10", "isNotBlank"]}
          errorMessages={["請輸入街道", "字數超過長度限制", "請輸入正確格式"]}
          label="街道(必填)"
          placeholder="街道(必填)"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 10,
          }}
          value={jobInfo.companyAddressStreet}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressStreet: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="companyAddressSection"
          name="companyAddressSection"
          validators={["isNumber", "maxNumber:99"]}
          errorMessages={["請輸入數字", "字數超過長度限制"]}
          label="段"
          fullWidth
          type="number"
          variant="outlined"
          value={jobInfo.companyAddressSection}
          onChange={(e) => {
            if (e.target.value.length < 3) {
              setJobInfo({
                ...jobInfo,
                companyAddressSection: e.target.value,
              });
            }
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="companyAddressLane"
          name="companyAddressLane"
          validators={["maxStringLength:6", "isNumberOrZh"]}
          errorMessages={["字數超過長度限制", "僅可輸入中文與數字"]}
          label="巷"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 6,
          }}
          value={jobInfo.companyAddressLane}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressLane: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="companyAddressAlley"
          name="companyAddressAlley"
          validators={["maxStringLength:6"]}
          errorMessages={["字數超過長度限制"]}
          label="弄"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 6,
          }}
          value={jobInfo.companyAddressAlley}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressAlley: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="companyAddressNo"
          name="companyAddressNo"
          validators={["matchRegexp:[0-9 -]+", "maxStringLength:10", "required", "isNotBlank"]}
          errorMessages={["請輸入數字", "字數超過長度限制", "請輸入號", "請輸入正確格式"]}
          label="號(必填)"
          placeholder="號(必填)"
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 10,
          }}
          value={jobInfo.companyAddressNo}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressNo: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="companyAddressFloor"
          name="companyAddressFloor"
          validators={["isNumericOrAlphabet", "maxStringLength:5"]}
          inputProps={{
            maxLength: 2,
          }}
          errorMessages={["請輸入數字或英文", "字數超過長度限制"]}
          label="樓"
          fullWidth
          variant="outlined"
          value={jobInfo.companyAddressFloor}
          onChange={(e) => {
            setJobInfo({
              ...jobInfo,
              companyAddressFloor: e.target.value,
            });
          }}
        />
      </Grid>
      <Grid item xs={4}>
        <TextValidator
          id="companyAddressRoom"
          name="companyAddressRoom"
          validators={["isNumber", "maxNumber:99999"]}
          errorMessages={["請輸入數字", "字數超過長度限制"]}
          label="之"
          fullWidth
          type="number"
          variant="outlined"
          value={jobInfo.companyAddressRoom}
          onChange={(e) => {
            if (e.target.value.length < 3) {
              setJobInfo({
                ...jobInfo,
                companyAddressRoom: e.target.value,
              });
            }
          }}
        />
      </Grid>
    </>
  );
});
